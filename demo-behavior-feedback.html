<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户行为反馈演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .demo-section {
            display: flex;
            gap: 20px;
        }
        
        .left-panel {
            flex: 1;
        }
        
        .right-panel {
            flex: 1;
        }
        
        .input-area {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 15px;
            min-height: 200px;
            background: #f8f9fa;
        }
        
        .input-box {
            width: 100%;
            min-height: 150px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: inherit;
            resize: vertical;
            background: white;
        }
        
        .feedback-display {
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            min-height: 200px;
            background: #f8fff8;
            font-family: monospace;
            font-size: 12px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .deepseek-buttons {
            margin: 15px 0;
        }
        
        ._6f28693 {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        
        ._7db3914 {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        ._5d271a3 {
            background: #dc3545;
            color: white;
            padding: 12px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            margin: 5px;
            width: 40px;
            height: 40px;
        }
        
        .ds-button {
            background: #6c757d;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .control-buttons {
            text-align: center;
            margin: 20px 0;
        }
        
        .control-buttons button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .start-btn {
            background: #28a745;
            color: white;
        }
        
        .stop-btn {
            background: #dc3545;
            color: white;
        }
        
        .clear-btn {
            background: #6c757d;
            color: white;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 用户行为反馈演示</h1>
        <div class="instructions">
            <h3>📋 使用说明：</h3>
            <ol>
                <li>点击"开始追踪"按钮启动用户行为监控</li>
                <li>在左侧区域进行各种操作（点击按钮、输入文本等）</li>
                <li>右侧会实时显示生成的带时间戳的自然语言反馈</li>
                <li>反馈会自动追加到输入框中，供手动发送</li>
            </ol>
        </div>
        
        <div id="status" class="status error">
            ❌ 行为追踪器未初始化
        </div>
        
        <div class="control-buttons">
            <button class="start-btn" onclick="startTracking()">▶️ 开始追踪</button>
            <button class="stop-btn" onclick="stopTracking()">⏹️ 停止追踪</button>
            <button class="clear-btn" onclick="clearAll()">🗑️ 清空所有</button>
        </div>
    </div>

    <div class="container">
        <div class="demo-section">
            <div class="left-panel">
                <h3>🎮 操作区域</h3>
                <div class="input-area">
                    <h4>📝 输入测试</h4>
                    <input type="text" placeholder="在这里输入文本..." style="width: 100%; margin: 5px 0; padding: 8px;">
                    <textarea placeholder="多行文本输入..." rows="3" style="width: 100%; margin: 5px 0; padding: 8px;"></textarea>
                    
                    <h4>🔘 DeepSeek按钮测试</h4>
                    <div class="deepseek-buttons">
                        <button class="_6f28693">发送按钮</button>
                        <button class="_7db3914">运行代码</button>
                        <button class="_5d271a3">×</button>
                        <button class="ds-button">DS按钮</button>
                        <div role="button" class="ds-button" style="display: inline-block;">Role按钮</div>
                    </div>
                    
                    <h4>✏️ 可编辑区域</h4>
                    <div contenteditable="true" style="border: 1px solid #ddd; padding: 10px; min-height: 60px; background: white;">
                        点击这里编辑内容...
                    </div>
                </div>
            </div>
            
            <div class="right-panel">
                <h3>📊 反馈显示</h3>
                <div class="input-area">
                    <h4>💬 模拟输入框（反馈会追加到这里）</h4>
                    <textarea id="mockInputBox" class="input-box" placeholder="用户行为反馈会自动追加到这里..."></textarea>
                    <button onclick="sendMessage()" style="background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; margin-top: 10px;">📤 手动发送</button>
                </div>
                
                <h4>📋 实时反馈日志</h4>
                <div id="feedbackLog" class="feedback-display">
                    等待用户操作...
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟消息类型常量
        window.MESSAGE_TYPES = {
            BEHAVIOR_DETECTED: 'BEHAVIOR_DETECTED',
            START_MONITORING: 'START_MONITORING',
            STOP_MONITORING: 'STOP_MONITORING',
            PROMPT_INJECTED: 'PROMPT_INJECTED'
        };

        // 模拟消息桥接器
        class MockMessageBridge {
            constructor() {
                this.listeners = new Map();
            }

            addListener(type, handler) {
                if (!this.listeners.has(type)) {
                    this.listeners.set(type, []);
                }
                this.listeners.get(type).push(handler);
                console.log('📡 添加监听器:', type);
            }

            sendMessage(type, data) {
                console.log('📤 发送消息:', type, data);
                
                // 处理不同类型的消息
                if (type === MESSAGE_TYPES.BEHAVIOR_DETECTED) {
                    handleBehaviorDetected(data);
                } else if (type === MESSAGE_TYPES.PROMPT_INJECTED) {
                    handlePromptInjected(data);
                }

                // 调用监听器
                if (this.listeners.has(type)) {
                    this.listeners.get(type).forEach(handler => {
                        try {
                            handler(data);
                        } catch (error) {
                            console.error('❌ 监听器执行错误:', error);
                        }
                    });
                }
            }

            sendStatusUpdate(data) {
                console.log('📊 状态更新:', data);
            }
        }

        // 模拟网站配置
        window.SiteConfigs = {
            getCurrentSiteConfig: () => ({
                name: 'Demo',
                getInputElement: () => document.getElementById('mockInputBox')
            })
        };

        // 模拟自动注入器
        class MockAutoInjector {
            async appendBehaviorFeedback(feedbackMessage) {
                console.log('📝 模拟追加反馈到输入框:', feedbackMessage);
                
                const inputBox = document.getElementById('mockInputBox');
                const currentValue = inputBox.value;
                
                if (currentValue.trim() === '') {
                    inputBox.value = feedbackMessage;
                } else {
                    inputBox.value = currentValue + '\n\n' + feedbackMessage;
                }
                
                // 滚动到底部
                inputBox.scrollTop = inputBox.scrollHeight;
                
                // 模拟发送PROMPT_INJECTED消息
                window.MessageBridge.sendMessage(MESSAGE_TYPES.PROMPT_INJECTED, {
                    message: feedbackMessage,
                    timestamp: Date.now(),
                    type: 'behavior_feedback',
                    autoSend: false
                });
            }
        }

        // 创建模拟实例
        window.MessageBridge = new MockMessageBridge();
        window.AutoInjector = new MockAutoInjector();

        // 处理行为检测
        function handleBehaviorDetected(data) {
            const logElement = document.getElementById('feedbackLog');
            const timestamp = new Date().toLocaleTimeString();
            
            let logEntry = `[${timestamp}] 检测到行为: ${data.description}`;
            if (data.type) {
                logEntry += ` (${data.type})`;
            }
            logEntry += '\n';
            
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 处理提示词注入
        function handlePromptInjected(data) {
            const logElement = document.getElementById('feedbackLog');
            const timestamp = new Date().toLocaleTimeString();
            
            let logEntry = `[${timestamp}] 反馈已追加到输入框\n`;
            
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 控制函数
        function startTracking() {
            if (window.BehaviorTracker) {
                window.BehaviorTracker.startTracking();
                updateStatus('✅ 行为追踪已启动', 'success');
            } else {
                updateStatus('❌ 行为追踪器未加载', 'error');
            }
        }

        function stopTracking() {
            if (window.BehaviorTracker) {
                window.BehaviorTracker.stopTracking();
                updateStatus('⏹️ 行为追踪已停止', 'error');
            } else {
                updateStatus('❌ 行为追踪器未加载', 'error');
            }
        }

        function clearAll() {
            document.getElementById('feedbackLog').textContent = '日志已清空...\n';
            document.getElementById('mockInputBox').value = '';
        }

        function sendMessage() {
            const inputBox = document.getElementById('mockInputBox');
            const message = inputBox.value.trim();
            
            if (message) {
                const logElement = document.getElementById('feedbackLog');
                const timestamp = new Date().toLocaleTimeString();
                logElement.textContent += `[${timestamp}] 📤 消息已发送: ${message.substring(0, 50)}...\n`;
                logElement.scrollTop = logElement.scrollHeight;
                
                // 清空输入框
                inputBox.value = '';
            }
        }

        function updateStatus(message, type) {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        // 页面加载完成后的初始化
        window.addEventListener('load', () => {
            console.log('📄 演示页面加载完成');

            setTimeout(() => {
                if (window.BehaviorTracker) {
                    updateStatus('✅ 行为追踪器已加载，可以开始演示', 'success');
                    // 自动启动追踪
                    startTracking();
                } else {
                    updateStatus('❌ 行为追踪器未加载，请确保扩展已安装', 'error');
                }
            }, 1000);
        });

        // 模拟主控制器的行为反馈生成
        function generateMockBehaviorFeedback(behaviorData) {
            const timestamp = Date.now();
            const timeInfo = {
                time: new Date(timestamp).toLocaleTimeString('zh-CN', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                }),
                relative: '刚刚'
            };

            return `🎯 ${behaviorData.description}
⏰ 时间: ${timeInfo.time} (${timeInfo.relative})
📍 位置: 演示页面
🗂️ 路径: ${behaviorData.target?.tagName || 'unknown'}.${behaviorData.target?.className || 'unknown'}
🌐 页面: 用户行为反馈演示 (${window.location.href})`;
        }
    </script>
</body>
</html>
