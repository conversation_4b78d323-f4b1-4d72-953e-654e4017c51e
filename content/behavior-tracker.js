/**
 * 用户行为追踪器 - 监控用户在渲染页面上的交互行为
 * 将用户操作转换为自然语言描述，用于LLM理解用户意图
 */

class BehaviorTracker {
  constructor() {
    this.isTracking = false;
    this.trackedContainers = new Set();
    this.actionQueue = [];
    this.lastActionTime = 0;
    this.actionThrottleDelay = 500; // 防抖延迟
    this.contentObserver = null; // 内容观察器

    this.init();
  }
  
  /**
   * 初始化行为追踪器 - 优化版本，减少不必要的监听
   */
  init() {
    console.log('👁️ 用户行为追踪器初始化中...'); // 启用日志用于调试

    // 只在需要时监听事件
    if (window.MessageBridge) {
      console.log('📡 MessageBridge存在，设置监听器');

      // 监听渲染完成事件
      window.MessageBridge.addListener(window.MESSAGE_TYPES.RENDER_COMPLETED, (data) => {
        console.log('🎨 收到渲染完成事件:', data);
        this.startTrackingContainer(data.container);
      });

      // 监听开始/停止追踪命令
      window.MessageBridge.addListener(window.MESSAGE_TYPES.START_MONITORING, () => {
        console.log('▶️ 收到开始监控命令');
        this.startTracking();
      });

      window.MessageBridge.addListener(window.MESSAGE_TYPES.STOP_MONITORING, () => {
        console.log('⏹️ 收到停止监控命令');
        this.stopTracking();
      });
    } else {
      console.warn('⚠️ MessageBridge不存在，无法设置监听器');
    }

    console.log('✅ 用户行为追踪器初始化完成'); // 启用日志用于调试
  }
  
  /**
   * 开始追踪指定容器 - 优化版本
   * @param {Object} containerInfo 容器信息
   */
  startTrackingContainer(containerInfo) {
    // 根据容器信息找到实际的DOM元素
    const container = this.findContainerElement(containerInfo);
    if (!container) {
      // console.warn('⚠️ 无法找到渲染容器元素'); // 禁用日志
      return;
    }

    if (this.trackedContainers.has(container)) {
      // console.log('ℹ️ 容器已在追踪中'); // 禁用日志
      return;
    }

    // console.log('🎯 开始追踪容器:', containerInfo); // 禁用日志
    this.trackedContainers.add(container);
    this.setupLightweightListeners(container); // 使用轻量级监听器
  }
  
  /**
   * 根据容器信息找到DOM元素
   * @param {Object} containerInfo 容器信息
   * @returns {Element|null} 找到的元素
   */
  findContainerElement(containerInfo) {
    // 尝试通过ID查找
    if (containerInfo.id) {
      const element = document.getElementById(containerInfo.id);
      if (element) return element;
    }
    
    // 尝试通过类名查找
    if (containerInfo.className) {
      const elements = document.getElementsByClassName(containerInfo.className);
      if (elements.length > 0) return elements[0];
    }
    
    // 尝试通过标签名和文本内容查找
    const elements = document.querySelectorAll(containerInfo.tagName);
    for (let element of elements) {
      if (element.textContent.includes(containerInfo.textContent.substring(0, 50))) {
        return element;
      }
    }
    
    return null;
  }
  
  /**
   * 为容器设置轻量级事件监听器 - 大幅减少性能开销
   * @param {Element} container 容器元素
   */
  setupLightweightListeners(container) {
    // 只监听关键的点击事件，使用节流
    let clickTimeout = null;
    container.addEventListener('click', (event) => {
      if (clickTimeout) return; // 防止频繁触发

      clickTimeout = setTimeout(() => {
        this.handleClickAction(event);
        clickTimeout = null;
      }, 200);
    }, { passive: true, capture: true });

    // 只监听重要的输入事件，大幅增加节流时间
    let inputTimeout = null;
    container.addEventListener('input', (event) => {
      if (inputTimeout) clearTimeout(inputTimeout);

      inputTimeout = setTimeout(() => {
        this.handleInputAction(event);
      }, 1000); // 增加到1秒
    }, { passive: true });

    // 移除悬停和滚动监听，这些会造成大量性能开销
    // 只保留表单提交监听
    container.addEventListener('submit', (event) => {
      this.handleSubmitAction(event);
    }, { passive: true });

    // console.log('👂 轻量级容器监听器已设置'); // 禁用日志
  }

  /**
   * 为容器设置事件监听器 - 保留原方法用于兼容性
   * @param {Element} container 容器元素
   */
  setupContainerListeners(container) {
    // 使用轻量级监听器
    this.setupLightweightListeners(container);
  }
  
  /**
   * 处理点击行为 - 优化版本
   * @param {Event} event 事件对象
   */
  handleClickAction(event) {
    const target = event.target;

    // 只处理重要的点击事件
    if (!this.isImportantElement(target)) {
      return;
    }

    const actionDescription = this.generateClickDescription(target);

    this.recordAction({
      type: 'click',
      target: this.getElementDescription(target),
      description: actionDescription,
      timestamp: Date.now(),
      coordinates: { x: event.clientX, y: event.clientY }
    });
  }

  /**
   * 处理iframe内的点击行为
   * @param {Event} event 事件对象
   * @param {Element} iframe iframe元素
   */
  handleIframeClickAction(event, iframe) {
    const target = event.target;

    // 只处理重要的点击事件
    if (!this.isImportantElement(target)) {
      return;
    }

    const actionDescription = this.generateClickDescription(target);

    this.recordAction({
      type: 'click',
      target: this.getElementDescription(target),
      description: `[iframe内] ${actionDescription}`,
      timestamp: Date.now(),
      coordinates: { x: event.clientX, y: event.clientY },
      iframe: {
        src: iframe.src,
        id: iframe.id,
        className: iframe.className
      }
    });
  }

  /**
   * 处理iframe内的输入行为
   * @param {Event} event 事件对象
   * @param {Element} iframe iframe元素
   */
  handleIframeInputAction(event, iframe) {
    const target = event.target;
    const value = target.value;

    // 只处理有意义的输入
    if (!value || value.length < 2) {
      return;
    }

    const actionDescription = this.generateInputDescription(target, value);

    this.recordAction({
      type: 'input',
      target: this.getElementDescription(target),
      description: `[iframe内] ${actionDescription}`,
      value: value.substring(0, 100), // 限制长度
      timestamp: Date.now(),
      iframe: {
        src: iframe.src,
        id: iframe.id,
        className: iframe.className
      }
    });
  }

  /**
   * 处理来自iframe的消息
   * @param {Object} data 消息数据
   * @param {Element} iframe iframe元素
   */
  handleIframeMessage(data, iframe) {
    // 处理来自iframe的用户行为消息
    if (data.type === 'user_action') {
      this.recordAction({
        type: data.actionType || 'unknown',
        target: data.target || {},
        description: `[iframe内] ${data.description || '用户操作'}`,
        timestamp: Date.now(),
        iframe: {
          src: iframe.src,
          id: iframe.id,
          className: iframe.className
        }
      });
    }
  }
  
  /**
   * 处理输入行为 - 优化版本
   * @param {Event} event 事件对象
   */
  handleInputAction(event) {
    const target = event.target;
    const value = target.value;

    // 只处理有意义的输入
    if (!value || value.length < 2) {
      return;
    }

    const actionDescription = this.generateInputDescription(target, value);

    this.recordAction({
      type: 'input',
      target: this.getElementDescription(target),
      description: actionDescription,
      value: value.substring(0, 100), // 限制长度
      timestamp: Date.now()
    });
  }
  
  /**
   * 处理表单提交行为 - 优化版本
   * @param {Event} event 事件对象
   */
  handleSubmitAction(event) {
    const form = event.target;
    const formData = new FormData(form);
    const actionDescription = this.generateSubmitDescription(form, formData);

    this.recordAction({
      type: 'submit',
      target: this.getElementDescription(form),
      description: actionDescription,
      formData: Object.fromEntries(formData),
      timestamp: Date.now()
    });
  }
  
  // 悬停和滚动处理已移除，以提高性能
  
  /**
   * 生成点击行为描述 - 增强版本，特别支持DeepSeek
   * @param {Element} target 目标元素
   * @returns {string} 行为描述
   */
  generateClickDescription(target) {
    const elementType = this.getElementType(target);
    const elementText = this.getElementText(target);
    const className = target.className || '';
    const role = target.getAttribute('role');

    // DeepSeek特定元素识别
    if (className.includes('_6f28693') || className.includes('ds-icon') || className.includes('_5d271a3')) {
      return `用户点击了DeepSeek发送按钮`;
    }

    if (className.includes('_7db3914')) {
      return `用户点击了DeepSeek运行按钮"${elementText}"`;
    }

    if (className.includes('ds-button') || className.includes('ds-icon-button')) {
      return `用户点击了DeepSeek按钮"${elementText}"`;
    }

    // 基于role属性的识别
    if (role === 'button') {
      if (elementText.includes('运行') || elementText.includes('Run')) {
        return `用户点击了运行按钮"${elementText}"`;
      } else if (elementText.includes('发送') || elementText.includes('Send')) {
        return `用户点击了发送按钮"${elementText}"`;
      } else {
        return `用户点击了按钮"${elementText}"`;
      }
    }

    // 传统元素识别
    if (target.tagName === 'BUTTON') {
      return `用户点击了按钮"${elementText}"`;
    } else if (target.tagName === 'A') {
      return `用户点击了链接"${elementText}"`;
    } else if (target.tagName === 'INPUT') {
      if (target.type === 'checkbox') {
        return `用户${target.checked ? '选中' : '取消选中'}了复选框"${elementText}"`;
      } else if (target.type === 'radio') {
        return `用户选择了单选按钮"${elementText}"`;
      } else if (target.type === 'submit') {
        return `用户点击了提交按钮"${elementText}"`;
      }
    } else if (target.tagName === 'SELECT') {
      return `用户点击了下拉选择框"${elementText}"`;
    }

    // 检查是否包含SVG图标（通常是按钮）
    if (target.querySelector('svg') || target.closest('[role="button"]')) {
      return `用户点击了图标按钮"${elementText}"`;
    }

    return `用户点击了${elementType}"${elementText}"`;
  }
  
  /**
   * 生成输入行为描述
   * @param {Element} target 目标元素
   * @param {string} value 输入值
   * @returns {string} 行为描述
   */
  generateInputDescription(target, value) {
    const placeholder = target.placeholder || '';
    const label = this.findLabelForInput(target);
    
    if (target.type === 'password') {
      return `用户在密码输入框中输入了内容`;
    } else if (target.type === 'email') {
      return `用户在邮箱输入框中输入了: ${value}`;
    } else if (target.type === 'number') {
      return `用户在数字输入框中输入了: ${value}`;
    } else if (target.tagName === 'TEXTAREA') {
      return `用户在文本区域中输入了: ${value.substring(0, 50)}${value.length > 50 ? '...' : ''}`;
    }
    
    const fieldName = label || placeholder || '输入框';
    return `用户在"${fieldName}"中输入了: ${value}`;
  }
  
  /**
   * 生成提交行为描述
   * @param {Element} form 表单元素
   * @param {FormData} formData 表单数据
   * @returns {string} 行为描述
   */
  generateSubmitDescription(form, formData) {
    const formName = form.name || form.id || '表单';
    const fieldCount = formData.entries().length;
    
    return `用户提交了"${formName}"，包含${fieldCount}个字段`;
  }
  
  /**
   * 生成悬停行为描述
   * @param {Element} target 目标元素
   * @returns {string} 行为描述
   */
  generateHoverDescription(target) {
    const elementType = this.getElementType(target);
    const elementText = this.getElementText(target);
    
    return `用户将鼠标悬停在${elementType}"${elementText}"上`;
  }
  
  /**
   * 记录用户行为 - 优化版本
   * @param {Object} action 行为对象
   */
  recordAction(action) {
    // 防止重复记录相同的行为，增加间隔时间
    const now = Date.now();
    if (now - this.lastActionTime < 500) return; // 增加到500ms

    this.lastActionTime = now;
    this.actionQueue.push(action);

    console.log('📝 记录用户行为:', action.description); // 启用日志用于调试

    // 发送行为数据
    if (window.MessageBridge) {
      console.log('📤 发送行为数据到MessageBridge:', action.type);
      window.MessageBridge.sendMessage(window.MESSAGE_TYPES.BEHAVIOR_DETECTED, action);
    } else {
      console.warn('⚠️ MessageBridge不存在，无法发送行为数据');
    }

    // 限制队列长度
    if (this.actionQueue.length > 20) { // 减少队列长度
      this.actionQueue.shift();
    }
  }
  
  /**
   * 获取元素描述
   * @param {Element} element 元素
   * @returns {Object} 元素描述
   */
  getElementDescription(element) {
    return {
      tagName: element.tagName,
      id: element.id,
      className: element.className,
      text: this.getElementText(element)
    };
  }
  
  /**
   * 获取元素类型描述
   * @param {Element} element 元素
   * @returns {string} 类型描述
   */
  getElementType(element) {
    const tagName = element.tagName.toLowerCase();
    const typeMap = {
      'button': '按钮',
      'a': '链接',
      'input': '输入框',
      'textarea': '文本区域',
      'select': '下拉选择框',
      'div': '区域',
      'span': '文本',
      'img': '图片',
      'form': '表单'
    };
    
    return typeMap[tagName] || tagName;
  }
  
  /**
   * 获取元素文本内容
   * @param {Element} element 元素
   * @returns {string} 文本内容
   */
  getElementText(element) {
    let text = element.textContent || element.value || element.placeholder || element.alt || '';
    return text.trim().substring(0, 30);
  }
  
  /**
   * 查找输入框的标签
   * @param {Element} input 输入框元素
   * @returns {string} 标签文本
   */
  findLabelForInput(input) {
    // 通过for属性查找
    if (input.id) {
      const label = document.querySelector(`label[for="${input.id}"]`);
      if (label) return label.textContent.trim();
    }
    
    // 查找父级label
    const parentLabel = input.closest('label');
    if (parentLabel) return parentLabel.textContent.trim();
    
    return '';
  }
  
  /**
   * 判断是否为重要元素 - 增强版本，特别支持DeepSeek
   * @param {Element} element 元素
   * @returns {boolean} 是否重要
   */
  isImportantElement(element) {
    const importantTags = ['button', 'a', 'input', 'select', 'textarea'];
    const tagName = element.tagName.toLowerCase();

    // 基本标签检查
    if (importantTags.includes(tagName)) {
      return true;
    }

    // 检查是否有点击事件
    if (element.onclick !== null) {
      return true;
    }

    // 检查通用可点击类名
    if (element.classList.contains('clickable')) {
      return true;
    }

    // 检查role属性
    const role = element.getAttribute('role');
    if (role === 'button' || role === 'textbox' || role === 'link') {
      return true;
    }

    // 检查contenteditable属性
    if (element.contentEditable === 'true') {
      return true;
    }

    // DeepSeek特定元素检查
    const className = element.className || '';
    const deepSeekClasses = [
      '_6f28693',      // DeepSeek发送按钮
      '_7db3914',      // DeepSeek运行按钮
      '_5d271a3',      // 新发现的DeepSeek按钮class
      'ds-button',     // DeepSeek按钮基类
      'ds-icon',       // DeepSeek图标
      'ds-icon-button', // DeepSeek图标按钮
      'send-button',   // 发送按钮
      'run-button'     // 运行按钮
    ];

    if (deepSeekClasses.some(cls => className.includes(cls))) {
      console.log('🎯 发现DeepSeek重要元素:', element.tagName, className);
      return true;
    }

    // 检查是否包含重要的子元素（如图标）
    if (element.querySelector('.ds-icon') || element.querySelector('svg')) {
      return true;
    }

    // 检查文本内容是否包含重要关键词
    const text = element.textContent?.trim().toLowerCase() || '';
    const importantKeywords = ['发送', 'send', '运行', 'run', '执行', 'execute', '提交', 'submit'];
    if (importantKeywords.some(keyword => text.includes(keyword))) {
      return true;
    }

    return false;
  }
  
  /**
   * 开始追踪 - 优化版本，减少全局监听
   */
  startTracking() {
    this.isTracking = true;
    console.log('▶️ 开始用户行为追踪'); // 启用日志用于调试

    // 只启动轻量级追踪，不进行全局监听
    this.startLightweightTracking();
  }

  /**
   * 启动轻量级追踪 - 大幅减少性能开销
   */
  startLightweightTracking() {
    console.log('🌐 启动轻量级页面行为追踪'); // 启用日志用于调试

    // 不追踪整个document，只追踪特定区域
    // this.setupContainerListeners(document); // 禁用全局监听

    // 只关注关键的LLM内容区域
    this.trackKeyLLMContent();
  }

  /**
   * 追踪关键LLM内容 - 轻量级版本，增强对DeepSeek iframe的支持
   */
  trackKeyLLMContent() {
    console.log('🎯 开始追踪关键LLM内容...');

    // 只查找最关键的容器，减少查询范围
    const keyContainers = [
      // 只关注按钮和输入框
      document.querySelectorAll('button'),
      document.querySelectorAll('input[type="text"], input[type="search"], textarea'),
      // 只关注可编辑内容
      document.querySelectorAll('[contenteditable="true"]'),
      // 特别关注DeepSeek的特定元素
      document.querySelectorAll('div[role="button"]'),
      document.querySelectorAll('.ds-button'),
      document.querySelectorAll('.ds-icon-button'),
      document.querySelectorAll('._6f28693'), // DeepSeek发送按钮
      document.querySelectorAll('._7db3914'), // DeepSeek运行按钮
      document.querySelectorAll('._5d271a3'), // 新发现的DeepSeek按钮class
      // 关注iframe内容
      document.querySelectorAll('iframe')
    ];

    keyContainers.forEach(nodeList => {
      // 限制处理数量，避免性能问题
      const limitedList = Array.from(nodeList).slice(0, 15); // 增加到15个
      limitedList.forEach(container => {
        if (!this.trackedContainers.has(container)) {
          console.log('🎯 发现关键LLM内容容器，开始追踪:', container.tagName, container.className);
          this.trackedContainers.add(container);
          this.setupLightweightListeners(container);

          // 如果是iframe，尝试监听其内容
          if (container.tagName === 'IFRAME') {
            this.setupIframeTracking(container);
          }
        }
      });
    });

    // 设置轻量级观察器
    this.setupLightweightContentObserver();
  }

  /**
   * 设置iframe追踪 - 处理DeepSeek等网站的iframe内容
   * @param {Element} iframe iframe元素
   */
  setupIframeTracking(iframe) {
    console.log('🖼️ 开始设置iframe追踪:', iframe.src);

    try {
      // 等待iframe加载完成
      iframe.addEventListener('load', () => {
        console.log('🖼️ iframe加载完成，尝试访问内容');

        try {
          // 尝试访问iframe内容（同源策略允许的情况下）
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
          if (iframeDoc) {
            console.log('🖼️ 成功访问iframe内容，开始监听');
            this.setupIframeContentListeners(iframeDoc, iframe);
          }
        } catch (error) {
          console.log('🖼️ 无法直接访问iframe内容（跨域限制）:', error.message);
          // 对于跨域iframe，使用postMessage通信
          this.setupCrossOriginIframeTracking(iframe);
        }
      });

      // 如果iframe已经加载完成
      if (iframe.contentDocument) {
        try {
          this.setupIframeContentListeners(iframe.contentDocument, iframe);
        } catch (error) {
          console.log('🖼️ iframe内容访问受限，使用跨域方案');
          this.setupCrossOriginIframeTracking(iframe);
        }
      }
    } catch (error) {
      console.error('🖼️ 设置iframe追踪失败:', error);
    }
  }

  /**
   * 设置iframe内容监听器
   * @param {Document} iframeDoc iframe文档
   * @param {Element} iframe iframe元素
   */
  setupIframeContentListeners(iframeDoc, iframe) {
    console.log('🖼️ 设置iframe内容监听器');

    // 为iframe内容设置点击监听
    iframeDoc.addEventListener('click', (event) => {
      console.log('🖼️ iframe内点击事件:', event.target);
      this.handleIframeClickAction(event, iframe);
    }, { passive: true, capture: true });

    // 为iframe内容设置输入监听
    iframeDoc.addEventListener('input', (event) => {
      console.log('🖼️ iframe内输入事件:', event.target);
      this.handleIframeInputAction(event, iframe);
    }, { passive: true });
  }

  /**
   * 设置跨域iframe追踪
   * @param {Element} iframe iframe元素
   */
  setupCrossOriginIframeTracking(iframe) {
    console.log('🖼️ 设置跨域iframe追踪');

    // 监听来自iframe的消息
    window.addEventListener('message', (event) => {
      // 检查消息来源
      if (event.source === iframe.contentWindow) {
        console.log('🖼️ 收到iframe消息:', event.data);
        this.handleIframeMessage(event.data, iframe);
      }
    });
  }

  /**
   * 设置轻量级内容观察器 - 大幅减少性能开销
   */
  setupLightweightContentObserver() {
    if (this.contentObserver) {
      this.contentObserver.disconnect();
    }

    // 使用节流的观察器，大幅减少触发频率
    let observerTimeout = null;

    this.contentObserver = new MutationObserver((mutations) => {
      // 清除之前的定时器
      if (observerTimeout) {
        clearTimeout(observerTimeout);
      }

      // 减少节流时间到1秒，提高响应性
      observerTimeout = setTimeout(() => {
        // 只检查关键的新增元素
        const hasKeyElements = mutations.some(mutation => {
          return Array.from(mutation.addedNodes).some(node =>
            node.nodeType === Node.ELEMENT_NODE &&
            (node.tagName === 'BUTTON' ||
             node.tagName === 'INPUT' ||
             node.tagName === 'TEXTAREA' ||
             node.tagName === 'IFRAME' ||
             node.contentEditable === 'true' ||
             node.classList.contains('ds-button') ||
             node.classList.contains('ds-icon-button') ||
             node.classList.contains('_6f28693') ||
             node.classList.contains('_7db3914') ||
             node.classList.contains('_5d271a3'))
          );
        });

        if (hasKeyElements) {
          console.log('🔍 检测到新的关键元素，重新追踪');
          this.trackKeyLLMContent();
        }
      }, 1000); // 减少到1秒
    });

    // 只观察子节点变化，不观察属性和文本变化
    this.contentObserver.observe(document.body, {
      childList: true,
      subtree: true, // 改为观察子树，以便捕获深层变化
      attributes: false,
      characterData: false
    });
  }

  /**
   * 判断是否为潜在的LLM内容
   * @param {Element} element 元素
   * @returns {boolean} 是否为LLM内容
   */
  isPotentialLLMContent(element) {
    const className = element.className || '';
    const tagName = element.tagName.toLowerCase();

    // 检查类名
    const llmIndicators = ['message', 'chat', 'response', 'code', 'pre', 'output'];
    const hasLLMClass = llmIndicators.some(indicator =>
      className.toLowerCase().includes(indicator)
    );

    // 检查标签
    const llmTags = ['pre', 'code', 'iframe'];
    const isLLMTag = llmTags.includes(tagName);

    // 检查是否包含交互元素
    const hasInteractiveElements = element.querySelectorAll('button, input, a, [onclick]').length > 0;

    return hasLLMClass || isLLMTag || hasInteractiveElements;
  }
  
  /**
   * 停止追踪 - 优化版本
   */
  stopTracking() {
    this.isTracking = false;
    // console.log('⏹️ 停止用户行为追踪'); // 禁用日志

    // 清理观察器
    if (this.contentObserver) {
      this.contentObserver.disconnect();
      this.contentObserver = null;
    }

    // 清理所有追踪的容器
    this.trackedContainers.clear();
  }
  
  /**
   * 获取行为队列
   * @returns {Array} 行为队列
   */
  getActionQueue() {
    return [...this.actionQueue];
  }
  
  /**
   * 清空行为队列 - 优化版本
   */
  clearActionQueue() {
    this.actionQueue = [];
    // console.log('🗑️ 行为队列已清空'); // 禁用日志
  }
}

// 创建行为追踪器实例
const behaviorTracker = new BehaviorTracker();

// 导出追踪器
window.BehaviorTracker = behaviorTracker;

// console.log('👁️ 用户行为追踪器模块已加载'); // 禁用日志
